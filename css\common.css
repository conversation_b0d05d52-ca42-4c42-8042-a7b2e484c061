/* Common Styles for All Pages */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
}

/* Header Styles */
.site-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 15px 20px;
    position: sticky;
    top: 0;
    z-index: 100;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

/* Header scrolled state */
.site-header.scrolled {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(15px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
    color: white;
    text-decoration: none;
    font-size: 22px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.logo:hover {
    transform: scale(1.05);
}

.logo img {
    width: 200px;
    height: 50px;
   
 
}



.nav-menu {
    display: flex;
    gap: 30px;
    list-style: none;
}

.nav-menu a {
    color: white;
    text-decoration: none;
    font-size: 15px;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 10px 18px;
    border-radius: 25px;
    position: relative;
}

.nav-menu a:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    width: 0;
    height: 2px;
    background: white;
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-menu a:hover::after {
    width: 80%;
}

/* User Points Display */
.user-points {
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(255, 255, 255, 0.2);
    padding: 10px 20px;
    border-radius: 25px;
    color: white;
    font-weight: bold;
    font-size: 16px;
    transition: all 0.3s ease;
}

.user-points:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.points-icon {
    font-size: 20px;
    animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

/* Footer Styles */
.site-footer {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    padding: 40px 20px 20px;
    margin-top: 60px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
}

.footer-links a:hover {
    color: white;
    transform: translateY(-1px);
}

.footer-text {
    font-size: 14px;
    opacity: 0.7;
}

/* Utility Classes */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.text-center {
    text-align: center;
}

.mb-20 {
    margin-bottom: 20px;
}

.mb-30 {
    margin-bottom: 30px;
}

.mt-20 {
    margin-top: 20px;
}

.mt-30 {
    margin-top: 30px;
}

/* Button Styles */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    font-weight: bold;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: #333;
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4);
}

/* Card Styles */
.card {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 50px rgba(0,0,0,0.15);
}

/* Mobile Menu */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 8px;
    transition: all 0.3s ease;
}

.mobile-menu-toggle:hover {
    transform: scale(1.1);
}

.mobile-menu {
    position: fixed;
    top: 0;
    right: -320px;
    width: 320px;
    height: 100vh;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(15px);
    transition: right 0.3s ease;
    z-index: 1001;
    padding: 80px 0 20px;
    box-shadow: -5px 0 20px rgba(0, 0, 0, 0.15);
    border-left: 1px solid rgba(0, 0, 0, 0.1);
    display: block;
}

.mobile-menu.active {
    right: 0;
}

.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.mobile-menu-overlay.active {
    opacity: 1;
    visibility: visible;
}

.mobile-nav-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.mobile-nav-menu li {
    margin-bottom: 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.mobile-nav-menu a {
    color: #333;
    text-decoration: none;
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;
    padding: 18px 25px;
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
    gap: 12px;
}

.mobile-nav-menu a:hover {
    background: linear-gradient(90deg, rgba(102, 126, 234, 0.1), transparent);
    border-left-color: #667eea;
    color: #667eea;
}

.mobile-nav-menu a:active {
    background: linear-gradient(90deg, rgba(102, 126, 234, 0.2), transparent);
}

.mobile-menu-close {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.05);
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #666;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.mobile-menu-close:hover {
    background: rgba(0, 0, 0, 0.1);
    transform: rotate(90deg);
    color: #333;
}

/* Responsive Design - Mobile Header */
@media (max-width: 768px) {
    .site-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        padding: 15px 0;
        transition: all 0.3s ease;
    }

    .site-header.scrolled {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-bottom: 1px solid rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(15px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }

    .header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        max-width: none;
        margin: 0;
        gap: 0;
        flex-wrap: nowrap;
        padding: 0 20px;
    }

    .logo {
        font-size: 18px;
        flex-shrink: 0;
        order: 1;
        color: white;
    }

    .logo:hover {
        transform: scale(1.05);
    }

    .logo img {
        width: 35px;
        height: 35px;
      
    }

    .nav-menu {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex !important;
        flex-shrink: 0;
        order: 3;
        background: none;
        border: none;
        color: white;
        font-size: 24px;
        width: 44px;
        height: 44px;
        border-radius: 8px;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }

    .mobile-menu-toggle:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: scale(1.1);
    }

    .user-points {
        display: flex;
        order: 2;
        font-size: 14px;
        padding: 8px 16px;
        gap: 8px;
    }

    .points-icon {
        font-size: 16px;
    }

    .footer-links {
        gap: 20px;
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .site-header {
        padding: 10px 15px;
    }
    
    .nav-menu {
        gap: 15px;
    }
    
    .nav-menu a {
        padding: 8px 15px;
        font-size: 14px;
    }
    
    .logo {
        font-size: 20px;
    }
    
    .logo img {
        width: 150px;
        height: 40px;
    }
    
    .user-points {
        padding: 8px 16px;
        font-size: 14px;
    }
    
    .footer-links {
        flex-direction: column;
        gap: 15px;
    }
}

/* Loading Animation */
.loading-spinner {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 25px;
    border-radius: 10px;
    color: white;
    font-weight: bold;
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.notification.error {
    background: linear-gradient(135deg, #dc3545, #c82333);
}

.notification.info {
    background: linear-gradient(135deg, #17a2b8, #138496);
}
