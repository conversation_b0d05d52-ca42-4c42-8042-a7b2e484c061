/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: white;
}

/* Header Styles (shared) */
.site-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 15px 20px;
    position: relative;
    top: 0;
    z-index: 100;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

/* Header scrolled state */
.site-header.scrolled {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    backdrop-filter: blur(15px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
    color: white;
    text-decoration: none;
    font-size: 20px;
    font-weight: bold;
}

/* .logo img {
    width: 150px;
    height: 40px;
} */

.nav-menu {
    display: flex;
    gap: 25px;
    list-style: none;
}

.nav-menu a {
    color: white;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 8px 16px;
    border-radius: 20px;
}

.nav-menu a:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.user-points {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.2);
    padding: 8px 16px;
    border-radius: 20px;
    color: white;
    font-weight: bold;
}

.points-icon {
    font-size: 18px;
}

/* Main Quiz Container */
.main-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: calc(100vh - 80px);
    padding: 20px;
}

.quiz-container {
    background: white;
    border-radius: 24px;
    padding: 35px;
    max-width: 600px;
    width: 100%;
    color: #333;
    box-shadow: 0 15px 50px rgba(0,0,0,0.2);
    position: relative;
}

/* Quiz Header */
.quiz-header {
    text-align: center;
    margin-bottom: 35px;
}

.quiz-icon {
    font-size: 56px;
    margin-bottom: 20px;
    display: block;
}

.quiz-title {
    font-size: 28px;
    font-weight: bold;
    margin-bottom: 12px;
    color: #2c3e50;
}

.quiz-description {
    color: #7f8c8d;
    font-size: 16px;
    line-height: 1.5;
}

/* Quiz Info */
.quiz-info {
    background: #f8f9fa;
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 25px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    font-size: 16px;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-label {
    color: #6c757d;
    font-weight: 500;
}

.info-value {
    font-weight: bold;
    color: #333;
}

/* Quiz Progress */
.quiz-progress {
    background: #f8f9fa;
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.progress-bar {
    flex: 1;
    height: 10px;
    background: #e9ecef;
    border-radius: 5px;
    margin: 0 20px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 5px;
    transition: width 0.5s ease;
}

/* Question Styles */
.question-container {
    margin-bottom: 35px;
}

.question-text {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 25px;
    color: #2c3e50;
    line-height: 1.6;
}

.options-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.option-button {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 16px;
    padding: 18px 24px;
    text-align: left;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
    color: #333;
    font-weight: 500;
}

.option-button:hover {
    border-color: #667eea;
    background: #f8f9ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
}

.option-button.selected {
    border-color: #667eea;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.option-button.correct {
    border-color: #28a745;
    background: #d4edda;
    color: #155724;
}

.option-button.incorrect {
    border-color: #dc3545;
    background: #f8d7da;
    color: #721c24;
}

/* Action Buttons */
.quiz-actions {
    display: flex;
    gap: 15px;
    margin-top: 35px;
}

.action-button {
    flex: 1;
    padding: 18px 24px;
    border: none;
    border-radius: 16px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

.next-button {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.next-button:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.next-button:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.back-button {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.back-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* Results */
.results-container {
    text-align: center;
    padding: 0px 20px;
}

.score-display {
    font-size: 56px;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 5px;
}

.score-text {
    font-size: 24px;
    margin-bottom: 35px;
    color: #333;
    line-height: 1.4;
}

.points-earned {
    background: linear-gradient(135deg, #ffd700, #ffb347);
    color: #333;
    padding: 15px 25px;
    border-radius: 20px;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 30px;
    display: inline-block;
}

/* Loading State */
.loading {
    text-align: center;
    padding: 50px 20px;
    color: #7f8c8d;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 50%;
    font-size: 20px;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    display: flex;
    align-items: center;
    justify-content: center;
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 25px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #5a6fd8, #6a42a0);
}

.back-to-top:active {
    transform: translateY(-1px);
}

@media (max-width: 768px) {
    .back-to-top {
        bottom: 20px;
        right: 20px;
        width: 45px;
        height: 45px;
        font-size: 18px;
    }
}

.loading h2 {
    font-size: 24px;
    margin-bottom: 15px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .quiz-container {
        margin: 10px;
        padding: 25px;
    }

    .quiz-title {
        font-size: 24px;
    }

    .question-text {
        font-size: 18px;
    }

    .score-display {
        font-size: 48px;
    }
}

@media (max-width: 480px) {
    .main-content {
        padding: 10px;
    }
    
    .quiz-container {
        padding: 20px;
    }
    
    .quiz-actions {
        flex-direction: column;
    }
}
